.PHONY: build run test clean deps

# 變數定義
APP_NAME=match-system
GO_VERSION=1.22.1

# 預設目標
all: deps test build

# 安裝依賴
deps:
	go mod tidy
	go mod download

# 建置應用程式
build:
	go build -o $(APP_NAME) .

# 執行應用程式
run:
	go run main.go

# 執行測試
test:
	go test -v ./...

# 執行測試並顯示覆蓋率
test-coverage:
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# 清理建置檔案
clean:
	rm -f $(APP_NAME)
	rm -f coverage.out
	rm -f coverage.html

# 格式化程式碼
fmt:
	go fmt ./...

# 檢查程式碼
lint:
	go vet ./...

# 開發模式 (自動重啟)
dev:
	go run main.go

# 建立 Docker 映像
docker-build:
	docker build -t $(APP_NAME) .

# 執行 Docker 容器
docker-run:
	docker run -p 8080:8080 $(APP_NAME)

# 顯示幫助
help:
	@echo "可用的命令:"
	@echo "  deps          - 安裝依賴"
	@echo "  build         - 建置應用程式"
	@echo "  run           - 執行應用程式"
	@echo "  test          - 執行測試"
	@echo "  test-coverage - 執行測試並顯示覆蓋率"
	@echo "  clean         - 清理建置檔案"
	@echo "  fmt           - 格式化程式碼"
	@echo "  lint          - 檢查程式碼"
	@echo "  dev           - 開發模式"
	@echo "  docker-build  - 建立 Docker 映像"
	@echo "  docker-run    - 執行 Docker 容器"
	@echo "  help          - 顯示此幫助訊息"
