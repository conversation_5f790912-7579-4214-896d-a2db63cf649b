version: '3.8'

services:
  # MySQL 資料庫
  mysql:
    image: mysql:8.0
    container_name: match-system-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: match_system
      MYSQL_USER: app_user
      MYSQL_PASSWORD: app_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - match-system-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # 撮合系統 API
  api:
    build: .
    container_name: match-system-api
    ports:
      - "8080:8080"
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=password
      - DB_NAME=match_system
    networks:
      - match-system-network
    restart: unless-stopped

  # phpMyAdmin (可選，用於資料庫管理)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: match-system-phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: password
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - match-system-network

volumes:
  mysql_data:

networks:
  match-system-network:
    driver: bridge
