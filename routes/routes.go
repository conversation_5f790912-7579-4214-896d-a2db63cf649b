package routes

import (
	"github.com/gin-gonic/gin"
	"go-test/handlers"
)

func SetupRoutes(handler *handlers.WagerHandler) *gin.Engine {
	r := gin.Default()
	
	api := r.Group("/api")
	{
		api.POST("/order", handler.CreateOrder)
		api.POST("/reserve", handler.Reserve)
		api.POST("/success", handler.Success)
		api.GET("/getmatchinglist", handler.GetMatchingList)
		api.POST("/cancel", handler.Cancel)
		api.GET("/getrejectedlist", handler.GetRejectedList)
		api.POST("/rejected", handler.Rejected)
	}
	
	return r
}
