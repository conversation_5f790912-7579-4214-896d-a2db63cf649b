#!/bin/bash

# 撮合系統 API 測試腳本
BASE_URL="http://localhost:8080/api"

echo "=== 撮合系統 API 測試 ==="

# 1. 測試增加出款委託單
echo "1. 測試增加出款委託單"
curl -X POST "$BASE_URL/order" \
  -H "Content-Type: application/json" \
  -d '{
    "WD_ID": 1234,
    "WD_Amount": 10000,
    "WD_Account": "802-**********"
  }' | jq .

echo -e "\n"

# 2. 測試預約入款
echo "2. 測試預約入款"
curl -X POST "$BASE_URL/reserve" \
  -H "Content-Type: application/json" \
  -d '{
    "Reserve_UserID": 10001,
    "Reserve_Amount": 10000
  }' | jq .

echo -e "\n"

# 3. 測試取撮合中委託單列表
echo "3. 測試取撮合中委託單列表"
curl -X GET "$BASE_URL/getmatchinglist" \
  -H "Content-Type: application/json" | jq .

echo -e "\n"

# 4. 測試撮合成功
echo "4. 測試撮合成功"
curl -X POST "$BASE_URL/success" \
  -H "Content-Type: application/json" \
  -d '{
    "WagerID": 1,
    "Reserve_UserID": 10001,
    "DEP_ID": 12389,
    "DEP_Amount": 10000
  }' | jq .

echo -e "\n"

# 5. 測試取失效單列表
echo "5. 測試取失效單列表"
curl -X GET "$BASE_URL/getrejectedlist" \
  -H "Content-Type: application/json" | jq .

echo -e "\n"

echo "=== 測試完成 ==="
