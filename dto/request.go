package dto

type CreateOrderRequest struct {
	WD_ID      int    `json:"WD_ID" binding:"required"`
	WD_Amount  int    `json:"WD_Amount" binding:"required"`
	WD_Account string `json:"WD_Account" binding:"required"`
}

type ReserveRequest struct {
	Reserve_UserID int `json:"Reserve_UserID" binding:"required"`
	Reserve_Amount int `json:"Reserve_Amount" binding:"required"`
}

type SuccessRequest struct {
	WagerID        uint `json:"WagerID" binding:"required"`
	Reserve_UserID int  `json:"Reserve_UserID" binding:"required"`
	DEP_ID         int  `json:"DEP_ID" binding:"required"`
	DEP_Amount     int  `json:"DEP_Amount" binding:"required"`
}

type GetMatchingListRequest struct {
	WagerID        uint `json:"WagerID"`
	Reserve_UserID int  `json:"Reserve_UserID"`
}

type RejectedRequest struct {
	WagerID        uint `json:"WagerID" binding:"required"`
	Reserve_UserID int  `json:"Reserve_UserID" binding:"required"`
}
