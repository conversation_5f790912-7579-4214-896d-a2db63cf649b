package dto

import "time"

type APIResponse struct {
	Success int         `json:"Success"`
	EorCode int         `json:"EorCode"`
	RunTime int         `json:"RunTime"`
	Data    interface{} `json:"Data"`
}

type OrderResponse struct {
	WID         uint      `json:"WID"`
	WD_ID       int       `json:"WD_ID"`
	WD_Amount   int       `json:"WD_Amount"`
	WD_Account  string    `json:"WD_Account"`
	WD_Datetime time.Time `json:"WD_Datetime"`
}

type ReserveResponse struct {
	WID        uint   `json:"WID"`
	WD_ID      int    `json:"WD_ID"`
	WD_Amount  int    `json:"WD_Amount"`
	WD_Account string `json:"WD_Account"`
}

type SuccessResponse struct {
	WID              uint      `json:"WID"`
	WD_ID            int       `json:"WD_ID"`
	WD_Amount        int       `json:"WD_Amount"`
	WD_Account       string    `json:"WD_Account"`
	Reserve_UserID   int       `json:"Reserve_UserID"`
	DEP_ID           int       `json:"DEP_ID"`
	DEP_Amount       int       `json:"DEP_Amount"`
	Finish_DateTime  time.Time `json:"Finish_DateTime"`
}

type MatchingListResponse struct {
	WID              uint      `json:"WID"`
	WD_ID            int       `json:"WD_ID"`
	WD_Amount        int       `json:"WD_Amount"`
	WD_Account       string    `json:"WD_Account"`
	Reserve_UserID   int       `json:"Reserve_UserID"`
	Reserve_DateTime time.Time `json:"Reserve_DateTime"`
}

type RejectedListResponse struct {
	WID         uint      `json:"WID"`
	WD_ID       int       `json:"WD_ID"`
	WD_Amount   int       `json:"WD_Amount"`
	WD_Account  string    `json:"WD_Account"`
	WD_Datetime time.Time `json:"WD_Datetime"`
}
