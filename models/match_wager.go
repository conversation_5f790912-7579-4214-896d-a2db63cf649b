package models

import (
	"time"
)

type MatchWager struct {
	WID              uint       `gorm:"primaryKey;autoIncrement" json:"WID"`
	WD_ID            int        `gorm:"not null" json:"WD_ID"`
	WD_Amount        int        `gorm:"not null" json:"WD_Amount"`
	WD_Account       string     `gorm:"size:15;not null" json:"WD_Account"`
	WD_Date          time.Time  `gorm:"type:date;not null" json:"WD_Date"`
	WD_DateTime      time.Time  `gorm:"not null" json:"WD_DateTime"`
	State            string     `gorm:"default:'Order';index" json:"State"`
	Reserve_UserID   *int       `json:"Reserve_UserID"`
	Reserve_DateTime *time.Time `json:"Reserve_DateTime"`
	DEP_ID           *int       `json:"DEP_ID"`
	DEP_Amount       *int       `json:"DEP_Amount"`
	Finish_DateTime  *time.Time `json:"Finish_DateTime"`
}

func (MatchWager) TableName() string {
	return "MatchWagers"
}
