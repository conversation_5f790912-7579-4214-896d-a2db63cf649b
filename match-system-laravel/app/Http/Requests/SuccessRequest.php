<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SuccessRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'WagerID' => 'required|integer',
            'Reserve_UserID' => 'required|integer',
            'DEP_ID' => 'required|integer',
            'DEP_Amount' => 'required|integer|min:1',
        ];
    }
}
