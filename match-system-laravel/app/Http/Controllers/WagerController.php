<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreateOrderRequest;
use App\Http\Requests\ReserveRequest;
use App\Http\Requests\SuccessRequest;
use App\Models\MatchWager;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

class WagerController extends Controller
{
    public function createOrder(CreateOrderRequest $request): JsonResponse
    {
        $startTime = microtime(true);

        try {
            $wager = MatchWager::create([
                'WD_ID' => $request->WD_ID,
                'WD_Amount' => $request->WD_Amount,
                'WD_Account' => $request->WD_Account,
                'WD_Date' => now()->toDateString(),
                'WD_DateTime' => now(),
                'State' => 'Order'
            ]);

            $responseData = [
                'WID' => $wager->WID,
                'WD_ID' => $wager->WD_ID,
                'WD_Amount' => $wager->WD_Amount,
                'WD_Account' => $wager->WD_Account,
                'WD_Datetime' => $wager->WD_DateTime->format('Y/m/d H:i:s')
            ];

            return $this->apiResponse(1, 0, $startTime, $responseData);
        } catch (Exception $e) {
            return $this->apiResponse(0, 4, $startTime, null);
        }
    }

    public function reserve(ReserveRequest $request): JsonResponse
    {
        $startTime = microtime(true);

        try {
            $wager = MatchWager::where('State', 'Order')
                ->where('WD_Amount', $request->Reserve_Amount)
                ->first();

            if (!$wager) {
                return $this->apiResponse(0, 2, $startTime, null);
            }

            $wager->update([
                'Reserve_UserID' => $request->Reserve_UserID,
                'Reserve_DateTime' => now(),
                'State' => 'Matching'
            ]);

            $responseData = [
                'WID' => $wager->WID,
                'WD_ID' => $wager->WD_ID,
                'WD_Amount' => $wager->WD_Amount,
                'WD_Account' => $wager->WD_Account
            ];

            return $this->apiResponse(1, 0, $startTime, $responseData);
        } catch (Exception $e) {
            return $this->apiResponse(0, 4, $startTime, null);
        }
    }

    public function success(SuccessRequest $request): JsonResponse
    {
        $startTime = microtime(true);

        try {
            $wager = MatchWager::where('WID', $request->WagerID)
                ->where('Reserve_UserID', $request->Reserve_UserID)
                ->where('State', 'Matching')
                ->first();

            if (!$wager) {
                return $this->apiResponse(0, 2, $startTime, null);
            }

            $wager->update([
                'DEP_ID' => $request->DEP_ID,
                'DEP_Amount' => $request->DEP_Amount,
                'Finish_DateTime' => now(),
                'State' => 'Success'
            ]);

            $responseData = [
                'WID' => $wager->WID,
                'WD_ID' => $wager->WD_ID,
                'WD_Amount' => $wager->WD_Amount,
                'WD_Account' => $wager->WD_Account,
                'Reserve_UserID' => $wager->Reserve_UserID,
                'DEP_ID' => $wager->DEP_ID,
                'DEP_Amount' => $wager->DEP_Amount,
                'Finish_DateTime' => $wager->Finish_DateTime->format('Y-m-d H:i:s')
            ];

            return $this->apiResponse(1, 0, $startTime, $responseData);
        } catch (Exception $e) {
            return $this->apiResponse(0, 4, $startTime, null);
        }
    }

    public function getMatchingList(Request $request): JsonResponse
    {
        $startTime = microtime(true);

        try {
            $query = MatchWager::where('State', 'Matching');

            if ($request->has('WagerID')) {
                $query->where('WID', $request->WagerID);
            }

            if ($request->has('Reserve_UserID')) {
                $query->where('Reserve_UserID', $request->Reserve_UserID);
            }

            $wagers = $query->get();

            $responseData = $wagers->map(function ($wager) {
                return [
                    'WID' => $wager->WID,
                    'WD_ID' => $wager->WD_ID,
                    'WD_Amount' => $wager->WD_Amount,
                    'WD_Account' => $wager->WD_Account,
                    'Reserve_UserID' => $wager->Reserve_UserID,
                    'Reserve_DateTime' => $wager->Reserve_DateTime->format('Y-m-d H:i:s')
                ];
            });

            return $this->apiResponse(1, 0, $startTime, $responseData);
        } catch (Exception $e) {
            return $this->apiResponse(0, 4, $startTime, null);
        }
    }

    public function cancel(): JsonResponse
    {
        $startTime = microtime(true);

        try {
            $wagers = MatchWager::where('State', 'Matching')->get();

            MatchWager::where('State', 'Matching')->update(['State' => 'Cancel']);

            $responseData = $wagers->map(function ($wager) {
                return [
                    'WID' => $wager->WID,
                    'WD_ID' => $wager->WD_ID,
                    'WD_Amount' => $wager->WD_Amount,
                    'WD_Account' => $wager->WD_Account,
                    'Reserve_UserID' => $wager->Reserve_UserID,
                    'Reserve_DateTime' => $wager->Reserve_DateTime->format('Y-m-d H:i:s')
                ];
            });

            return $this->apiResponse(1, 0, $startTime, $responseData);
        } catch (Exception $e) {
            return $this->apiResponse(0, 4, $startTime, null);
        }
    }

    public function getRejectedList(): JsonResponse
    {
        $startTime = microtime(true);

        try {
            $wagers = MatchWager::where('State', 'Rejected')->get();

            $responseData = $wagers->map(function ($wager) {
                return [
                    'WID' => $wager->WID,
                    'WD_ID' => $wager->WD_ID,
                    'WD_Amount' => $wager->WD_Amount,
                    'WD_Account' => $wager->WD_Account,
                    'WD_Datetime' => $wager->WD_DateTime->format('Y/m/d H:i:s')
                ];
            });

            return $this->apiResponse(1, 0, $startTime, $responseData);
        } catch (Exception $e) {
            return $this->apiResponse(0, 4, $startTime, null);
        }
    }

    public function rejected(Request $request): JsonResponse
    {
        $startTime = microtime(true);

        try {
            $request->validate([
                'WagerID' => 'required|integer',
                'Reserve_UserID' => 'required|integer'
            ]);

            $wager = MatchWager::where('WID', $request->WagerID)
                ->where('Reserve_UserID', $request->Reserve_UserID)
                ->first();

            if (!$wager) {
                return $this->apiResponse(0, 2, $startTime, null);
            }

            $wager->update(['State' => 'Rejected']);

            $responseData = [
                'WID' => $wager->WID,
                'WD_ID' => $wager->WD_ID,
                'WD_Amount' => $wager->WD_Amount,
                'WD_Account' => $wager->WD_Account,
                'Reserve_UserID' => $wager->Reserve_UserID
            ];

            return $this->apiResponse(1, 0, $startTime, $responseData);
        } catch (Exception $e) {
            return $this->apiResponse(0, 4, $startTime, null);
        }
    }

    private function apiResponse(int $success, int $errorCode, float $startTime, $data): JsonResponse
    {
        $runTime = (int)((microtime(true) - $startTime) * 1000);

        return response()->json([
            'Success' => $success,
            'EorCode' => $errorCode,
            'RunTime' => $runTime,
            'Data' => $data
        ]);
    }
}
