<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MatchWager extends Model
{
    protected $table = 'match_wagers';
    protected $primaryKey = 'WID';

    protected $fillable = [
        'WD_ID',
        'WD_Amount',
        'WD_Account',
        'WD_Date',
        'WD_DateTime',
        'State',
        'Reserve_UserID',
        'Reserve_DateTime',
        'DEP_ID',
        'DEP_Amount',
        'Finish_DateTime'
    ];

    protected $casts = [
        'WD_Date' => 'date',
        'WD_DateTime' => 'datetime',
        'Reserve_DateTime' => 'datetime',
        'Finish_DateTime' => 'datetime',
    ];
}
