<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('match_wagers', function (Blueprint $table) {
            $table->id('WID');
            $table->integer('WD_ID');
            $table->smallInteger('WD_Amount');
            $table->string('WD_Account', 15);
            $table->date('WD_Date');
            $table->dateTime('WD_DateTime');
            $table->enum('State', ['Order', 'Rejected', 'Matching', 'Success', 'Cancel'])->default('Order');
            $table->integer('Reserve_UserID')->nullable();
            $table->dateTime('Reserve_DateTime')->nullable();
            $table->integer('DEP_ID')->nullable();
            $table->smallInteger('DEP_Amount')->nullable();
            $table->dateTime('Finish_DateTime')->nullable();

            $table->index('State');
            $table->index('WD_DateTime');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('match_wagers');
    }
};
