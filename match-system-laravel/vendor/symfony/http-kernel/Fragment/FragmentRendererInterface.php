<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpKernel\Fragment;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Controller\ControllerReference;

/**
 * Interface implemented by all rendering strategies.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface FragmentRendererInterface
{
    /**
     * Renders a URI and returns the Response content.
     */
    public function render(string|ControllerReference $uri, Request $request, array $options = []): Response;

    /**
     * Gets the name of the strategy.
     */
    public function getName(): string;
}
