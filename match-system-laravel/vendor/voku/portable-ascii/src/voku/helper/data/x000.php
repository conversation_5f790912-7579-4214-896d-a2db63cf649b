<?php return ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ' ', '!', '"', '#', '$', '%', '&', '\'', '(', ')', '*', '+', ',', '-', '.', '/', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ':', ';', '<', '=', '>', '?', '@', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '[', '\\', ']', '^', '_', '`', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '{', '|', '}', '~', '', 'EUR',  // "\xc2\x80" => "\xe2\x82\xac" => EURO SIGN
    '', ',', 'f', ',,',   // "\xc2\x84" => "\xe2\x80\x9e" => DOUBLE LOW-9 QUOTATION MARK
    '...',  // "\xc2\x85" => "\xe2\x80\xa6" =>  HORIZONTAL ELLIPSIS
    '+', '++',   // "\xc2\x87" => "\xe2\x80\xa1" => DOUBLE DAGGER
    '^', '%0',   // "\xc2\x89" => "\xe2\x80\xb0" => PER MILLE SIGN
    'S', '<', 'OE',   // "\xc2\x8c" => "\xc5\x92" => LATIN CAPITAL LIGATURE OE
    '', 'Z', '', '', '\'',   // "\xc2\x91" => "\xe2\x80\x98" => LEFT SINGLE QUOTATION MARK
    '\'',   // "\xc2\x92" => "\xe2\x80\x99" => RIGHT SINGLE QUOTATION MARK
    '"', '"', '*', '-', '--',   // "\xc2\x97" => "\xe2\x80\x94" => EM DASH
    '~', 'tm', 's', '>', 'oe', '', 'z', 'Y', ' ', '!', 'C/', 'PS', '$?', 'Y=', '|', 'SS', '"', '(c)', 'a', '<<', '!', '', '(r)', '-', 'deg', '+-', '2', '3', '\'', 'u', 'P', '*', ',', '1', 'o', '>>', '1/4', '1/2', '3/4', '?', 'A', 'A', 'A', 'A', // Not "AE" - used in languages other than German
    'A', 'A', 'AE', 'C', 'E', 'E', 'E', 'E', 'I', 'I', 'I', 'I', 'D', 'N', 'O', 'O', 'O', 'O', // Not "OE" - used in languages other than German
    'O', 'x', 'O', 'U', 'U', 'U', // Not "UE" - used in languages other than German
    'U', 'Y', 'Th', 'ss', 'a', 'a', 'a', 'a', // Not "ae" - used in languages other than German
    'a', 'a', 'ae', 'c', 'e', 'e', 'e', 'e', 'i', 'i', 'i', 'i', 'd', 'n', 'o', 'o', 'o', 'o', // Not "oe" - used in languages other than German
    'o', '/', 'o', 'u', 'u', 'u', // Not "ue" - used in languages other than German
    'u', 'y', 'th', 'y', ];
