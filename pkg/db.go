package pkg

import "fmt"

type dbStruct struct {
	dbname string
	host   string
	port   string
}

func NewDBStruct() *dbStruct {
	return &dbStruct{}
}

func (d *dbStruct) SetdbStructValue(dbname string, host string, port string) {
	d.dbname = dbname
	d.host = host
	d.port = port
}

func (d *dbStruct) DBConnect() {
	fmt.Println("db connect .....")
	fmt.Println("db name: ", d.dbname, "db host: ", d.host, "port: ", d.port)
}
