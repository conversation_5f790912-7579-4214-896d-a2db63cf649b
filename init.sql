-- 撮合系統資料庫初始化腳本

-- 建立資料庫 (如果不存在)
CREATE DATABASE IF NOT EXISTS match_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用資料庫
USE match_system;

-- 建立 MatchWagers 表
CREATE TABLE IF NOT EXISTS `MatchWagers` (
    `WID` INT AUTO_INCREMENT PRIMARY KEY COMMENT '委託單 ID',
    
    `WD_ID` INT NOT NULL COMMENT '出款單號',
    `WD_Amount` SMALLINT NOT NULL COMMENT '出款金額',
    `WD_Account` VARCHAR(15) NOT NULL COMMENT '出款帳戶',
    `WD_Date` DATE NOT NULL COMMENT '出款單日期',
    `WD_DateTime` DATETIME NOT NULL COMMENT '出款建立時間',

    `State` ENUM('Order', 'Rejected', 'Matching', 'Success', 'Cancel') NOT NULL DEFAULT 'Order' COMMENT '狀態',
    
    `Reserve_UserID` INT DEFAULT NULL COMMENT '會員預約入款 ID',
    `Reserve_DateTime` DATETIME DEFAULT NULL COMMENT '預約入款時間',
    
    `DEP_ID` INT DEFAULT NULL COMMENT '入款單號',
    `DEP_Amount` SMALLINT DEFAULT NULL COMMENT '入款金額',
    `Finish_DateTime` DATETIME DEFAULT NULL COMMENT '完成時間',

    INDEX `idx_state` (`State`),
    INDEX `idx_wd_datetime` (`WD_DateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='撮合系統委託單表';

-- 插入測試資料
INSERT INTO `MatchWagers` (
    `WD_ID`, `WD_Amount`, `WD_Account`, `WD_Date`, `WD_DateTime`, `State`
) VALUES 
(1001, 5000, '802-**********', CURDATE(), NOW(), 'Order'),
(1002, 10000, '802-**********', CURDATE(), NOW(), 'Order'),
(1003, 15000, '802-**********', CURDATE(), NOW(), 'Matching'),
(1004, 8000, '802-**********', CURDATE(), NOW(), 'Success'),
(1005, 12000, '802-**********', CURDATE(), NOW(), 'Rejected');

-- 更新測試資料中的撮合狀態
UPDATE `MatchWagers` SET 
    `Reserve_UserID` = 10001, 
    `Reserve_DateTime` = NOW() 
WHERE `WID` = 3;

UPDATE `MatchWagers` SET 
    `Reserve_UserID` = 10002, 
    `Reserve_DateTime` = DATE_SUB(NOW(), INTERVAL 1 HOUR),
    `DEP_ID` = 2001,
    `DEP_Amount` = 8000,
    `Finish_DateTime` = DATE_SUB(NOW(), INTERVAL 30 MINUTE)
WHERE `WID` = 4;
