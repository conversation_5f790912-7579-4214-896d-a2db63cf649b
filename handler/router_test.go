package handler

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
)

// func randomKeyValue() (string, string) {
// 	key := fmt.Sprintf("key-%d", rand.Intn(1000))
// 	value := fmt.Sprintf("value-%d", rand.Intn(1000))
// 	return key, value
// }

func testSet(t *testing.T) {
	req := struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}{
		Key:   "Alice",
		Value: "20",
	}
	jsonData, _ := json.Marshal(req)
	resp, err := http.Post("http://localhost:8080/set", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		t.Fatalf("http curl set data error: %v", err)
	}
	body, _ := io.ReadAll(resp.Body)
	s := struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}{}

	json.Unmarshal(body, &s)
	if s.Key != "Alice" {
		t.Fatalf("key error : %v", s.Key)
	}
	if s.Value != "20" {
		t.Fatalf("value error : %v", s.Value)
	}

	// s := struct {
	// 	Key   string `json:"key"`
	// 	Value string `json:"value"`
	// }{}
	// body, err := io.ReadAll(resp.Body)
	// if err != nil {
	// 	t.Fatalf("get response body error %v", err)
	// }
	//
	// err = json.Unmarshal(body, &s)
	// if err != nil {
	// 	t.Fatalf("json decode error %v", err)
	// }
	//
}

func testGet() {
}

func TestInit(t *testing.T) {
	fmt.Println("開始測式")
	// set test
	httptest.NewServer(Router())
}
