package handlers

import (
	"net/http"
	"time"
	"github.com/gin-gonic/gin"
	"go-test/dto"
	"go-test/services"
)

type WagerHandler struct {
	service *services.WagerService
}

func NewWagerHandler(service *services.WagerService) *WagerHandler {
	return &WagerHandler{service: service}
}

func (h *WagerHandler) CreateOrder(c *gin.Context) {
	start := time.Now()
	var req dto.CreateOrderRequest
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.APIResponse{
			Success: 0,
			EorCode: 1,
			RunTime: int(time.Since(start).Milliseconds()),
			Data:    nil,
		})
		return
	}
	
	wager, err := h.service.CreateOrder(req.WD_ID, req.WD_Amount, req.WD_Account)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.APIResponse{
			Success: 0,
			EorCode: 4,
			RunTime: int(time.Since(start).Milliseconds()),
			Data:    nil,
		})
		return
	}
	
	response := dto.OrderResponse{
		WID:         wager.WID,
		WD_ID:       wager.WD_ID,
		WD_Amount:   wager.WD_Amount,
		WD_Account:  wager.WD_Account,
		WD_Datetime: wager.WD_DateTime,
	}
	
	c.JSON(http.StatusOK, dto.APIResponse{
		Success: 1,
		EorCode: 0,
		RunTime: int(time.Since(start).Milliseconds()),
		Data:    response,
	})
}

func (h *WagerHandler) Reserve(c *gin.Context) {
	start := time.Now()
	var req dto.ReserveRequest
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.APIResponse{
			Success: 0,
			EorCode: 1,
			RunTime: int(time.Since(start).Milliseconds()),
			Data:    nil,
		})
		return
	}
	
	wager, err := h.service.Reserve(req.Reserve_UserID, req.Reserve_Amount)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.APIResponse{
			Success: 0,
			EorCode: 4,
			RunTime: int(time.Since(start).Milliseconds()),
			Data:    nil,
		})
		return
	}
	
	response := dto.ReserveResponse{
		WID:        wager.WID,
		WD_ID:      wager.WD_ID,
		WD_Amount:  wager.WD_Amount,
		WD_Account: wager.WD_Account,
	}
	
	c.JSON(http.StatusOK, dto.APIResponse{
		Success: 1,
		EorCode: 0,
		RunTime: int(time.Since(start).Milliseconds()),
		Data:    response,
	})
}

func (h *WagerHandler) Success(c *gin.Context) {
	start := time.Now()
	var req dto.SuccessRequest
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.APIResponse{
			Success: 0,
			EorCode: 1,
			RunTime: int(time.Since(start).Milliseconds()),
			Data:    nil,
		})
		return
	}
	
	wager, err := h.service.Success(req.WagerID, req.Reserve_UserID, req.DEP_ID, req.DEP_Amount)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.APIResponse{
			Success: 0,
			EorCode: 4,
			RunTime: int(time.Since(start).Milliseconds()),
			Data:    nil,
		})
		return
	}
	
	response := dto.SuccessResponse{
		WID:             wager.WID,
		WD_ID:           wager.WD_ID,
		WD_Amount:       wager.WD_Amount,
		WD_Account:      wager.WD_Account,
		Reserve_UserID:  *wager.Reserve_UserID,
		DEP_ID:          *wager.DEP_ID,
		DEP_Amount:      *wager.DEP_Amount,
		Finish_DateTime: *wager.Finish_DateTime,
	}
	
	c.JSON(http.StatusOK, dto.APIResponse{
		Success: 1,
		EorCode: 0,
		RunTime: int(time.Since(start).Milliseconds()),
		Data:    response,
	})
}

func (h *WagerHandler) GetMatchingList(c *gin.Context) {
	start := time.Now()
	var req dto.GetMatchingListRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		req = dto.GetMatchingListRequest{}
	}

	wagers, err := h.service.GetMatchingList(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.APIResponse{
			Success: 0,
			EorCode: 4,
			RunTime: int(time.Since(start).Milliseconds()),
			Data:    nil,
		})
		return
	}

	var responses []dto.MatchingListResponse
	for _, wager := range wagers {
		if wager.Reserve_UserID != nil && wager.Reserve_DateTime != nil {
			responses = append(responses, dto.MatchingListResponse{
				WID:              wager.WID,
				WD_ID:            wager.WD_ID,
				WD_Amount:        wager.WD_Amount,
				WD_Account:       wager.WD_Account,
				Reserve_UserID:   *wager.Reserve_UserID,
				Reserve_DateTime: *wager.Reserve_DateTime,
			})
		}
	}

	c.JSON(http.StatusOK, dto.APIResponse{
		Success: 1,
		EorCode: 0,
		RunTime: int(time.Since(start).Milliseconds()),
		Data:    responses,
	})
}

func (h *WagerHandler) Cancel(c *gin.Context) {
	start := time.Now()

	wagers, err := h.service.Cancel()
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.APIResponse{
			Success: 0,
			EorCode: 4,
			RunTime: int(time.Since(start).Milliseconds()),
			Data:    nil,
		})
		return
	}

	var responses []dto.MatchingListResponse
	for _, wager := range wagers {
		if wager.Reserve_UserID != nil && wager.Reserve_DateTime != nil {
			responses = append(responses, dto.MatchingListResponse{
				WID:              wager.WID,
				WD_ID:            wager.WD_ID,
				WD_Amount:        wager.WD_Amount,
				WD_Account:       wager.WD_Account,
				Reserve_UserID:   *wager.Reserve_UserID,
				Reserve_DateTime: *wager.Reserve_DateTime,
			})
		}
	}

	c.JSON(http.StatusOK, dto.APIResponse{
		Success: 1,
		EorCode: 0,
		RunTime: int(time.Since(start).Milliseconds()),
		Data:    responses,
	})
}

func (h *WagerHandler) GetRejectedList(c *gin.Context) {
	start := time.Now()

	wagers, err := h.service.GetRejectedList()
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.APIResponse{
			Success: 0,
			EorCode: 4,
			RunTime: int(time.Since(start).Milliseconds()),
			Data:    nil,
		})
		return
	}

	var responses []dto.RejectedListResponse
	for _, wager := range wagers {
		responses = append(responses, dto.RejectedListResponse{
			WID:         wager.WID,
			WD_ID:       wager.WD_ID,
			WD_Amount:   wager.WD_Amount,
			WD_Account:  wager.WD_Account,
			WD_Datetime: wager.WD_DateTime,
		})
	}

	c.JSON(http.StatusOK, dto.APIResponse{
		Success: 1,
		EorCode: 0,
		RunTime: int(time.Since(start).Milliseconds()),
		Data:    responses,
	})
}

func (h *WagerHandler) Rejected(c *gin.Context) {
	start := time.Now()
	var req dto.RejectedRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.APIResponse{
			Success: 0,
			EorCode: 1,
			RunTime: int(time.Since(start).Milliseconds()),
			Data:    nil,
		})
		return
	}

	wager, err := h.service.Rejected(req.WagerID, req.Reserve_UserID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.APIResponse{
			Success: 0,
			EorCode: 4,
			RunTime: int(time.Since(start).Milliseconds()),
			Data:    nil,
		})
		return
	}

	response := dto.ReserveResponse{
		WID:        wager.WID,
		WD_ID:      wager.WD_ID,
		WD_Amount:  wager.WD_Amount,
		WD_Account: wager.WD_Account,
	}

	c.JSON(http.StatusOK, dto.APIResponse{
		Success: 1,
		EorCode: 0,
		RunTime: int(time.Since(start).Milliseconds()),
		Data:    response,
	})
}
