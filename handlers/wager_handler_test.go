package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"go-test/dto"
	"go-test/models"
	"go-test/services"
)

func setupTestDB() *gorm.DB {
	db, _ := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	db.AutoMigrate(&models.MatchWager{})
	return db
}

func setupTestHandler() *WagerHandler {
	db := setupTestDB()
	service := services.NewWagerService(db)
	return NewWagerHandler(service)
}

func TestCreateOrder(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler := setupTestHandler()
	
	router := gin.New()
	router.POST("/api/order", handler.CreateOrder)
	
	requestBody := dto.CreateOrderRequest{
		WD_ID:      1234,
		WD_Amount:  10000,
		WD_Account: "802-**********",
	}
	
	jsonBody, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", "/api/order", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)
	
	assert.Equal(t, http.StatusOK, w.Code)
	
	var response dto.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 1, response.Success)
	assert.Equal(t, 0, response.EorCode)
}

func TestCreateOrderInvalidRequest(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler := setupTestHandler()
	
	router := gin.New()
	router.POST("/api/order", handler.CreateOrder)
	
	invalidBody := `{"invalid": "data"}`
	req, _ := http.NewRequest("POST", "/api/order", bytes.NewBufferString(invalidBody))
	req.Header.Set("Content-Type", "application/json")
	
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)
	
	assert.Equal(t, http.StatusBadRequest, w.Code)
	
	var response dto.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 0, response.Success)
	assert.Equal(t, 1, response.EorCode)
}

func TestReserve(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler := setupTestHandler()
	
	wager := &models.MatchWager{
		WD_ID:       1234,
		WD_Amount:   10000,
		WD_Account:  "802-**********",
		WD_Date:     time.Now(),
		WD_DateTime: time.Now(),
		State:       "Order",
	}
	handler.service.CreateOrder(wager.WD_ID, wager.WD_Amount, wager.WD_Account)
	
	router := gin.New()
	router.POST("/api/reserve", handler.Reserve)
	
	requestBody := dto.ReserveRequest{
		Reserve_UserID: 10001,
		Reserve_Amount: 10000,
	}
	
	jsonBody, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", "/api/reserve", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)
	
	assert.Equal(t, http.StatusOK, w.Code)
	
	var response dto.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 1, response.Success)
	assert.Equal(t, 0, response.EorCode)
}
