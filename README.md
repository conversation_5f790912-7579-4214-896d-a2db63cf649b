# 撮合系統 API

## 專案概述
這是一個使用 Go + Gin + GORM + MySQL 開發的撮合系統 API 服務，提供出款委託單管理、預約入款、撮合處理等功能。

## 技術架構
- **後端框架**: Gin
- **ORM**: GORM
- **資料庫**: MySQL
- **架構模式**: Clean Architecture (分層架構)

## 專案結構
```
go-test/
├── main.go                 # 主程式入口
├── config/
│   └── database.go        # 資料庫配置
├── models/
│   └── match_wager.go     # 資料模型
├── handlers/
│   └── wager_handler.go   # API 處理器
├── services/
│   └── wager_service.go   # 業務邏輯
├── dto/
│   ├── request.go         # 請求結構體
│   └── response.go        # 回應結構體
├── routes/
│   └── routes.go          # 路由設定
├── go.mod                 # 依賴管理
└── README.md              # 專案說明
```

## API 端點

### 1. 增加出款委託單
- **URL**: `POST /api/order`
- **Request**:
```json
{
  "WD_ID": 1234,
  "WD_Amount": 10000,
  "WD_Account": "802-**********"
}
```

### 2. 預約入款
- **URL**: `POST /api/reserve`
- **Request**:
```json
{
  "Reserve_UserID": 10001,
  "Reserve_Amount": 10000
}
```

### 3. 撮合成功
- **URL**: `POST /api/success`
- **Request**:
```json
{
  "WagerID": 1,
  "Reserve_UserID": 10001,
  "DEP_ID": 12389,
  "DEP_Amount": 10000
}
```

### 4. 取撮合中委託單列表
- **URL**: `GET /api/getmatchinglist`
- **Request**:
```json
{
  "WagerID": 1,
  "Reserve_UserID": 10001
}
```

### 5. 取消撮合中委託單
- **URL**: `POST /api/cancel`
- **Request**: 無參數

### 6. 取失效單列表轉三方
- **URL**: `GET /api/getrejectedlist`
- **Request**: 無參數

### 7. 出款委託單轉失效
- **URL**: `POST /api/rejected`
- **Request**:
```json
{
  "WagerID": 1,
  "Reserve_UserID": 10001
}
```

## 資料庫設定
在 `config/database.go` 中修改資料庫連線字串：
```go
dsn := "root:password@tcp(localhost:3306)/match_system?charset=utf8mb4&parseTime=True&loc=Local"
```

## 快速開始

### 使用 Makefile
```bash
# 查看所有可用命令
make help

# 安裝依賴
make deps

# 執行測試
make test

# 建置應用程式
make build

# 啟動服務
make run
```

### 使用 Docker Compose
```bash
# 啟動完整環境 (包含 MySQL 和 phpMyAdmin)
docker-compose up -d

# 查看日誌
docker-compose logs -f api

# 停止服務
docker-compose down
```

### 手動啟動
```bash
# 安裝依賴
go mod tidy

# 啟動服務
go run main.go
```

服務將在 `http://localhost:8080` 啟動。

## 回應格式
所有 API 都使用統一的回應格式：
```json
{
  "Success": 1,
  "EorCode": 0,
  "RunTime": 50,
  "Data": {}
}
```

- `Success`: 1 表示成功，0 表示失敗
- `EorCode`: 錯誤碼 (0: 成功, 1: 參數錯誤, 4: 資料庫錯誤)
- `RunTime`: 執行時間 (毫秒)
- `Data`: 回應資料

## 開發工具

### 測試
```bash
# 執行所有測試
make test

# 執行測試並顯示覆蓋率
make test-coverage
```

### 程式碼品質
```bash
# 格式化程式碼
make fmt

# 檢查程式碼
make lint
```

### API 測試
```bash
# 使用提供的測試腳本
./test_api.sh
```

## Docker 支援

### 建立 Docker 映像
```bash
make docker-build
```

### 執行 Docker 容器
```bash
make docker-run
```

### 使用 Docker Compose
專案包含完整的 Docker Compose 配置：
- **API 服務**: 撮合系統 API (port 8080)
- **MySQL**: 資料庫服務 (port 3306)
- **phpMyAdmin**: 資料庫管理介面 (port 8081)

## 環境變數
可以透過環境變數配置資料庫連線：
```bash
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=match_system
```

## 資料庫表結構
系統會自動建立 `MatchWagers` 表，包含以下欄位：
- WID: 委託單 ID (主鍵)
- WD_ID, WD_Amount, WD_Account: 出款資訊
- State: 狀態 (Order, Rejected, Matching, Success, Cancel)
- Reserve_UserID, Reserve_DateTime: 預約資訊
- DEP_ID, DEP_Amount, Finish_DateTime: 入款資訊

## 專案特色
- ✅ Clean Architecture 分層架構
- ✅ GORM ORM 支援
- ✅ 資料庫連線池配置
- ✅ 統一的 API 回應格式
- ✅ 完整的單元測試
- ✅ Docker 容器化支援
- ✅ Makefile 自動化工具
- ✅ 環境變數配置
- ✅ 錯誤處理和日誌記錄
