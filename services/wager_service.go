package services

import (
	"errors"
	"time"
	"gorm.io/gorm"
	"go-test/models"
	"go-test/dto"
)

type WagerService struct {
	db *gorm.DB
}

func NewWagerService(db *gorm.DB) *WagerService {
	return &WagerService{db: db}
}

func (s *WagerService) CreateOrder(wdID int, wdAmount int, wdAccount string) (*models.MatchWager, error) {
	now := time.Now()
	wager := &models.MatchWager{
		WD_ID:       wdID,
		WD_Amount:   wdAmount,
		WD_Account:  wdAccount,
		WD_Date:     now,
		WD_DateTime: now,
		State:       "Order",
	}
	
	if err := s.db.Create(wager).Error; err != nil {
		return nil, err
	}
	
	return wager, nil
}

func (s *WagerService) Reserve(reserveUserID int, reserveAmount int) (*models.MatchWager, error) {
	var wager models.MatchWager
	
	if err := s.db.Where("state = ? AND wd_amount = ?", "Order", reserveAmount).First(&wager).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("no matching order found")
		}
		return nil, err
	}
	
	now := time.Now()
	wager.Reserve_UserID = &reserveUserID
	wager.Reserve_DateTime = &now
	wager.State = "Matching"
	
	if err := s.db.Save(&wager).Error; err != nil {
		return nil, err
	}
	
	return &wager, nil
}

func (s *WagerService) Success(wagerID uint, reserveUserID int, depID int, depAmount int) (*models.MatchWager, error) {
	var wager models.MatchWager
	
	if err := s.db.Where("wid = ? AND reserve_user_id = ? AND state = ?", wagerID, reserveUserID, "Matching").First(&wager).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("matching wager not found")
		}
		return nil, err
	}
	
	now := time.Now()
	wager.DEP_ID = &depID
	wager.DEP_Amount = &depAmount
	wager.Finish_DateTime = &now
	wager.State = "Success"
	
	if err := s.db.Save(&wager).Error; err != nil {
		return nil, err
	}
	
	return &wager, nil
}

func (s *WagerService) GetMatchingList(req dto.GetMatchingListRequest) ([]models.MatchWager, error) {
	var wagers []models.MatchWager
	query := s.db.Where("state = ?", "Matching")
	
	if req.WagerID != 0 {
		query = query.Where("wid = ?", req.WagerID)
	}
	if req.Reserve_UserID != 0 {
		query = query.Where("reserve_user_id = ?", req.Reserve_UserID)
	}
	
	if err := query.Find(&wagers).Error; err != nil {
		return nil, err
	}
	
	return wagers, nil
}

func (s *WagerService) Cancel() ([]models.MatchWager, error) {
	var wagers []models.MatchWager
	
	if err := s.db.Where("state = ?", "Matching").Find(&wagers).Error; err != nil {
		return nil, err
	}
	
	if err := s.db.Model(&models.MatchWager{}).Where("state = ?", "Matching").Update("state", "Cancel").Error; err != nil {
		return nil, err
	}
	
	return wagers, nil
}

func (s *WagerService) GetRejectedList() ([]models.MatchWager, error) {
	var wagers []models.MatchWager
	
	if err := s.db.Where("state = ?", "Rejected").Find(&wagers).Error; err != nil {
		return nil, err
	}
	
	return wagers, nil
}

func (s *WagerService) Rejected(wagerID uint, reserveUserID int) (*models.MatchWager, error) {
	var wager models.MatchWager
	
	if err := s.db.Where("wid = ? AND reserve_user_id = ?", wagerID, reserveUserID).First(&wager).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("wager not found")
		}
		return nil, err
	}
	
	wager.State = "Rejected"
	
	if err := s.db.Save(&wager).Error; err != nil {
		return nil, err
	}
	
	return &wager, nil
}
