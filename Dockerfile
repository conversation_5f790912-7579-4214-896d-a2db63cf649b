# 使用官方 Go 映像作為建置環境
FROM golang:1.22.1-alpine AS builder

# 設定工作目錄
WORKDIR /app

# 安裝必要的套件
RUN apk add --no-cache git

# 複製 go mod 檔案
COPY go.mod go.sum ./

# 下載依賴
RUN go mod download

# 複製原始碼
COPY . .

# 建置應用程式
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o match-system .

# 使用輕量級的 alpine 映像作為執行環境
FROM alpine:latest

# 安裝 ca-certificates 以支援 HTTPS
RUN apk --no-cache add ca-certificates

# 設定工作目錄
WORKDIR /root/

# 從建置階段複製執行檔
COPY --from=builder /app/match-system .

# 暴露埠號
EXPOSE 8080

# 執行應用程式
CMD ["./match-system"]
