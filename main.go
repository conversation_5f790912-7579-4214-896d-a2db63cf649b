package main

import (
	"log"
	"go-test/config"
	"go-test/handlers"
	"go-test/models"
	"go-test/routes"
	"go-test/services"
)

func main() {
	db, err := config.InitDB()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	if err := db.AutoMigrate(&models.MatchWager{}); err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	wagerService := services.NewWagerService(db)
	wagerHandler := handlers.NewWagerHandler(wagerService)

	r := routes.SetupRoutes(wagerHandler)

	log.Println("Server starting on :8080")
	if err := r.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
